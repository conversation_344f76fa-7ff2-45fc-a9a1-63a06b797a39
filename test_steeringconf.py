#!/usr/bin/env python3
"""
Test script for SteeringConf aggregation algorithms
"""

import logging
import numpy as np
from typing import List, Tuple, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class SteeringConfAggregator:
    """
    Implements the SteeringConf aggregation methodology from <PERSON> et al. (2025)
    "Calibrating LLM Confidence with Semantic Steering: A Multi-Prompt Aggregation Framework"
    """
    
    @staticmethod
    def calculate_answer_consistency(decisions: List[str]) -> Tuple[str, float]:
        """
        Calculate answer consistency (κ_ans) from the research paper.
        
        Args:
            decisions: List of decision strings from different steering prompts
            
        Returns:
            Tuple of (dominant_decision, consistency_score)
        """
        if not decisions:
            return "NEUTRAL", 0.0
            
        # Count frequency of each decision
        decision_counts = {}
        for decision in decisions:
            decision_counts[decision] = decision_counts.get(decision, 0) + 1
        
        # Find dominant decision and its frequency
        dominant_decision = max(decision_counts, key=decision_counts.get)
        dominant_count = decision_counts[dominant_decision]
        
        # Calculate consistency score (frequency of dominant answer)
        consistency_score = dominant_count / len(decisions)
        
        return dominant_decision, consistency_score
    
    @staticmethod
    def calculate_confidence_consistency(confidences: List[float]) -> Tuple[float, float, float]:
        """
        Calculate confidence consistency (κ_conf) from the research paper.
        
        Args:
            confidences: List of confidence scores from different steering prompts
            
        Returns:
            Tuple of (mean_confidence, std_confidence, consistency_score)
        """
        if not confidences:
            return 0.0, 0.0, 0.0
            
        # Calculate mean and standard deviation
        mean_conf = float(np.mean(confidences))
        std_conf = float(np.std(confidences))
        
        # Calculate confidence consistency score: κ_conf = 1 / (1 + σ_c/μ_c)
        # This penalizes high variance relative to mean confidence
        if mean_conf > 0:
            consistency_score = 1.0 / (1.0 + (std_conf / mean_conf))
        else:
            consistency_score = 0.0
            
        return mean_conf, std_conf, consistency_score
    
    @staticmethod
    def calculate_calibrated_confidence(mean_confidence: float, 
                                      answer_consistency: float, 
                                      confidence_consistency: float) -> float:
        """
        Calculate the final calibrated confidence from the research paper.
        
        Formula: c(x) = μ_c · κ_ans · κ_conf
        
        Args:
            mean_confidence: Average confidence across steering prompts
            answer_consistency: Answer consistency score (κ_ans)
            confidence_consistency: Confidence consistency score (κ_conf)
            
        Returns:
            Calibrated confidence score
        """
        calibrated_conf = mean_confidence * answer_consistency * confidence_consistency
        return max(0.0, min(10.0, calibrated_conf))  # Clamp to valid range
    
    @staticmethod
    def select_steered_answer(decisions: List[str], 
                            confidences: List[float], 
                            calibrated_confidence: float,
                            steering_labels: List[str]) -> Tuple[str, int]:
        """
        Select the steered answer closest to the calibrated confidence.
        
        Args:
            decisions: List of decisions from steering prompts
            confidences: List of confidence scores from steering prompts
            calibrated_confidence: The calculated calibrated confidence
            steering_labels: Labels for steering prompts (for mapping back)
            
        Returns:
            Tuple of (selected_decision, selected_index)
        """
        if not decisions or not confidences:
            return "NEUTRAL", 0
            
        # Find the confidence score closest to calibrated confidence
        min_distance = float('inf')
        selected_index = 0
        
        for i, conf in enumerate(confidences):
            distance = abs(conf - calibrated_confidence)
            if distance < min_distance:
                min_distance = distance
                selected_index = i
                
        return decisions[selected_index], selected_index

def test_steeringconf_aggregation():
    """Test the SteeringConf aggregation algorithms"""
    logging.info("Testing SteeringConf aggregation algorithms...")
    
    aggregator = SteeringConfAggregator()
    
    # Test answer consistency
    decisions = ["BULLISH", "BULLISH", "NEUTRAL", "BULLISH", "BEARISH"]
    dominant, consistency = aggregator.calculate_answer_consistency(decisions)
    logging.info(f"Answer consistency test: dominant={dominant}, consistency={consistency:.3f}")
    assert dominant == "BULLISH"
    assert consistency == 0.6  # 3 out of 5
    
    # Test confidence consistency
    confidences = [7.5, 8.0, 6.5, 7.8, 5.2]
    mean_conf, std_conf, conf_consistency = aggregator.calculate_confidence_consistency(confidences)
    logging.info(f"Confidence consistency test: mean={mean_conf:.3f}, std={std_conf:.3f}, consistency={conf_consistency:.3f}")
    assert abs(mean_conf - 7.0) < 0.1  # Should be around 7.0
    
    # Test calibrated confidence
    calibrated = aggregator.calculate_calibrated_confidence(mean_conf, consistency, conf_consistency)
    logging.info(f"Calibrated confidence: {calibrated:.3f}")
    assert calibrated > 0  # Should be positive
    
    # Test steered answer selection
    selected_decision, selected_index = aggregator.select_steered_answer(
        decisions, confidences, calibrated, ["very_cautious", "cautious", "vanilla", "confident", "very_confident"]
    )
    logging.info(f"Selected decision: {selected_decision} (index {selected_index})")
    assert selected_decision in decisions
    assert 0 <= selected_index < len(decisions)
    
    logging.info("✅ All SteeringConf aggregation tests passed!")

def test_edge_cases():
    """Test edge cases for SteeringConf algorithms"""
    logging.info("Testing edge cases...")
    
    aggregator = SteeringConfAggregator()
    
    # Test empty inputs
    dominant, consistency = aggregator.calculate_answer_consistency([])
    assert dominant == "NEUTRAL"
    assert consistency == 0.0
    
    mean_conf, std_conf, conf_consistency = aggregator.calculate_confidence_consistency([])
    assert mean_conf == 0.0
    assert std_conf == 0.0
    assert conf_consistency == 0.0
    
    # Test single input
    dominant, consistency = aggregator.calculate_answer_consistency(["BULLISH"])
    assert dominant == "BULLISH"
    assert consistency == 1.0
    
    # Test identical confidences (zero variance)
    mean_conf, std_conf, conf_consistency = aggregator.calculate_confidence_consistency([5.0, 5.0, 5.0])
    assert mean_conf == 5.0
    assert std_conf == 0.0
    assert conf_consistency == 1.0  # Perfect consistency
    
    logging.info("✅ All edge case tests passed!")

if __name__ == "__main__":
    test_steeringconf_aggregation()
    test_edge_cases()
    logging.info("🎉 All SteeringConf tests completed successfully!")
