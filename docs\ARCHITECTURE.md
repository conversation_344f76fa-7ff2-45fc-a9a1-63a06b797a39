## TradingAgents Architecture and MCP Integration Plan

### Overview
This system implements a multi‑agent trading research and decision pipeline inspired by the TradingAgents framework:
- Analysts (technical, economist, sentiment, news) produce structured AnalystReport documents
- Researchers (Bull vs Bear) debate and a Facilitator determines the prevailing view
- Chief Trader synthesizes a TradingDecision
- Risk Management Team scores risks from different perspectives
- Fund Manager issues final approval
- AgentState records all artifacts and mirrors them into a structured protocol dictionary for audit/export

### Code Topology
- Option Colab.py
  - Data layer
    - DataFetcher: fetches SPY,^VIX,^IRX and computes HV; optional MCP override
    - EnhancedDataFetcher: assembles comprehensive data bundle used by agents
  - Agents
    - TechnicalAnalystAgent (Sperandeo features)
    - EconomistSentimentAnalystAgent (FRED + VIX)
    - AlphaVantageSentimentAnalyst (NEWS_SENTIMENT – financial_markets,economy_macro)
    - AlphaVantageNewsAnalyst (NEWS_SENTIMENT – economy_monetary,economy_fiscal)
    - DebateF<PERSON><PERSON>tat<PERSON>, Chief<PERSON><PERSON><PERSON>, RiskManagementTeam, FundManager
  - State
    - AgentState: stores analyst_reports, research_debate, trader_decision, risk_assessments, final_decision and mirrors them into `state.protocol`
  - Orchestration
    - EnhancedBacktestEngine: daily loop performing all phases and logging results

### Time Horizon (Six Months Everywhere)
- ANALYSIS_WINDOW_DAYS = 126 (≈ six months of trading days)
- SPY,^VIX,^IRX fetched for a buffer range; tailed to last 126 days for analysis
- SPY OHLCV for Sperandeo features: 126 days
- FRED normalization: lookback_periods=6 (months)
- Macro trend window: 180 calendar days
- Alpha Vantage NEWS_SENTIMENT: time_from = asof − 190 days, time_to = asof, limit=1000

### Data Bundle Schema (EnhancedDataFetcher.get_comprehensive_data)
- market_data: pd.DataFrame
  - Columns: SPY,^VIX,^IRX Close; HV column for SPY (annualized %, HV_WINDOW=21)
- fred_data: pd.DataFrame
  - Aligned daily frame of selected FRED series
- spy_ohlcv: pd.DataFrame
  - SPY OHLCV for 126 days
- fred_processed: pd.DataFrame
  - Combined raw + 3‑month averages + normalized indicators (−1..1) and composite_score
- fred_signals: dict
  - {
    date: str,
    values: {symbol_normalized: float},
    trends: {symbol_normalized: float},
    strength: {symbol_normalized: str},
    data_age: {symbol_normalized: int}
  }
- sperandeo_features: pd.DataFrame
- asof_date: datetime

### Structured Protocol (AgentState.protocol)
- analyst_reports: {analyst_type: AnalystReport as dict}
- research_debate: {prevailing_view, confidence, key_points}
- trader_decision: TradingDecision as dict
- risk_assessments: [RiskAssessment as dict]
- final_decision: dict returned by FundManager

### Agent Output Schemas (selected)
- AnalystReport
  - analyst_type: str; ticker: str; date: str
  - key_findings: list[str]
  - metrics: dict[str, any]
  - recommendation: str (domain‑specific)
  - confidence: float
  - raw_data: optional dict (small sample only)
- TradingDecision
  - action: str (e.g., BUY_CALL, BUY_PUT, NO_TRADE)
  - strategy: str; dte: int; confidence: float; reasoning: str

### MCP Integration Plan
Phase 1 (implemented hook):
- DataFetcher accepts `mcp_context` with method:
  - get_market_data(current_date) -> dict matching Data Bundle Schema keys: market_data, fred_data, spy_ohlcv
- Pass your MCP implementation into EnhancedBacktestEngine(start, end, mcp_context=YourMCPClient())

Phase 2 (short‑term additions):
- Analytics tools (deterministic, prompt‑saving)
  - compute_sperandeo_features(ohlcv) -> pd.DataFrame
  - fred_normalize(end_date, lookback_months=6) -> pd.DataFrame
  - fred_signals(df, lookback_days=180) -> dict (like fred_signals above)
- State/audit tools
  - store_protocol(protocol: dict) -> None
  - store_results(day_result: dict) -> None

Phase 3 (options integration):
- get_options_chain(symbol: str, date: str) -> DataFrame
- compute_greeks(surface or chain) -> DataFrame
- Provide to ChiefTrader for strategy refinement

### Integration Steps
1) Implement YourMCPClient with get_market_data(current_date) returning the minimal bundle keys: market_data, fred_data, spy_ohlcv
2) Optionally add analytics and state/audit methods (Phase 2)
3) Instantiate engine:
```python
engine = EnhancedBacktestEngine("2024-01-01", "2024-02-01", mcp_context=YourMCPClient())
engine.run_backtest()
```

### Configuration and Keys
- API keys
  - OPENROUTER_API_KEY (env recommended)
  - FRED_API_KEY (env recommended)
  - ALPHAVANTAGE_API_KEY (env or default value)
- Topic filters (Alpha Vantage)
  - Sentiment: financial_markets,economy_macro
  - News: economy_monetary,economy_fiscal

### Rate Limiting and Robustness
- Alpha Vantage NEWS_SENTIMENT: use limit=1000 over a 190‑day window; gracefully handle errors with empty feeds
- pandas_datareader FRED: retry/robust fill and validation are in place; normalization uses 3‑month rolling averages
- yfinance: tailed to 126 trading days post‑fetch to ensure consistent horizons

### Auditing and Export (optional next step)
- Export `state.protocol` per day to artifacts/ as JSON for full traceability
- Include debate history and analyst reports in the export payload

### Security Considerations
- Prefer environment variables for keys
- Avoid logging sensitive tokens
- Keep raw_data samples small within AnalystReport to prevent excessive PII/telemetry surfacing

