# Confidence Calibration System

## Overview

This document describes the confidence calibration system implemented in the TradingAgents framework, based on research findings from "Calibrating LLM Confidence with Semantic Steering: A Multi-Prompt Aggregation Framework" by <PERSON> et al. (2025).

## Research Foundation

The confidence calibration system addresses the well-documented problem of LLM overconfidence by implementing:

1. **Confidence Steering**: Using "very cautious" prompting to reduce overconfidence
2. **Structured Confidence Expression**: Standardized 0-100 confidence scoring
3. **Uncertainty Acknowledgment**: Explicit recognition of limitations and conflicting signals
4. **Domain-Specific Calibration**: Tailored confidence criteria for each agent type

## Confidence Scoring Framework

### Scale: 0-100%

- **High Confidence (80-100)**: Clear, unambiguous signals across multiple indicators, high data quality, strong historical precedent
- **Medium Confidence (50-79)**: Generally consistent signals with minor conflicts, adequate data quality, some historical support
- **Low Confidence (20-49)**: Mixed or conflicting signals, limited data quality, weak historical precedent
- **Very Low Confidence (0-19)**: Highly conflicting signals, poor data quality, no clear precedent

## Agent-Specific Calibration Guidelines

### Technical Analyst (Sperandeo Methodology)

**High Confidence (80-100):**
- All three Sperandeo rules (1-2-3, 2B, Four-day) align
- Clear trendline break with volume confirmation
- Strong momentum in consistent direction

**Medium Confidence (50-79):**
- Two of three Sperandeo rules confirm
- Trendline break with moderate volume
- Generally consistent technical picture

**Low Confidence (20-49):**
- Only one Sperandeo rule confirms
- Unclear trendline status or weak volume
- Mixed momentum signals

### Economist Sentiment Analyst

**High Confidence (80-100):**
- All FRED indicators point in same direction
- Strong historical correlation with market moves
- VIX and economic data align

**Medium Confidence (50-79):**
- Most FRED indicators align
- Moderate historical correlation
- Some divergence between VIX and economic data

**Low Confidence (20-49):**
- FRED indicators give mixed signals
- Weak historical correlation
- Significant divergence between indicators

### Sentiment/News Analysts

**High Confidence (80-100):**
- Large sample size (>500 articles)
- Consistent sentiment across sources
- High relevance and reliability of sources

**Medium Confidence (50-79):**
- Moderate sample size (100-500 articles)
- Generally consistent sentiment
- Good source reliability

**Low Confidence (20-49):**
- Small sample size (<100 articles)
- Mixed sentiment signals
- Questionable source reliability

## Cautious Steering Implementation

### Core Principles

1. **Risk Awareness**: "You are making important financial decisions, thus you should avoid giving wrong analysis with high confidence"
2. **Cautious Default**: "Be very cautious and tend to give lower confidence when signals are mixed or data is limited"
3. **Uncertainty Acknowledgment**: "Acknowledge uncertainty explicitly when facing conflicting indicators"

### Agent-Specific Steering

- **Technical Analyst**: Cautious when Sperandeo rules conflict or volume doesn't confirm
- **Economist**: Cautious when FRED indicators give mixed signals
- **Sentiment Analysts**: Cautious when sample sizes are small or sources unreliable
- **Chief Trader**: Very cautious when analyst confidence levels are low or conflicting

## Enhanced Output Formats

### Structured Analysis Format

```
ANALYSIS: [Detailed step-by-step analysis]

KEY FINDINGS WITH CONFIDENCE:
1. [Finding 1] - Confidence: [0-100]%
2. [Finding 2] - Confidence: [0-100]%

OVERALL RECOMMENDATION AND CONFIDENCE: [Recommendation], [Overall confidence 0-100]%

UNCERTAINTY ACKNOWLEDGMENT: [Explicit statement of uncertainties or limitations]
```

### JSON Output Enhancement

```json
{
    "key_findings": [
        {"finding": "text", "confidence": 85}
    ],
    "recommendation": "BULLISH/BEARISH/NEUTRAL",
    "confidence": 0.78,
    "uncertainty_factors": ["factor1", "factor2"],
    "confidence_breakdown": {"rule_1": 90, "rule_2": 65}
}
```

## Chief Trader Confidence Aggregation

The Chief Trader implements a sophisticated confidence aggregation methodology:

1. **Analyst Confidence Assessment**: Reviews each analyst's confidence level
2. **Consensus Analysis**: Determines agreement or conflict in recommendations
3. **Risk-Adjusted Confidence**: Lowers confidence when facing uncertainty
4. **Cautious Steering**: Considers "No Trade" when aggregate confidence is low (<60%)

### Enhanced Decision Output

```json
{
    "decision": "BULLISH/BEARISH/NEUTRAL",
    "strategy": "specific options strategy",
    "confidence": 7,
    "analyst_confidence_summary": "brief summary of each analyst's confidence",
    "uncertainty_factors": "list of significant uncertainties",
    "risk_assessment": "assessment of potential downside"
}
```

## Implementation Benefits

1. **Reduced Overconfidence**: Addresses the documented LLM overconfidence problem
2. **Better Risk Management**: Uncertainty acknowledgment improves risk assessment
3. **Enhanced Transparency**: Clear confidence expression improves decision auditability
4. **Improved Reliability**: Calibrated confidence leads to better trading decisions

## Integration with Existing Framework

The confidence calibration system integrates seamlessly with existing components:

- **AnalystReport**: Already includes confidence field (0-1 scale)
- **AgentState**: Automatically displays confidence in analyst summaries
- **Risk Management**: Confidence scores inform risk assessment
- **Sperandeo Integration**: Maintains technical analysis methodology while adding confidence
- **FRED Framework**: Preserves economic indicator analysis with enhanced confidence

## Validation and Testing

The enhanced prompts maintain full backward compatibility:

- No syntax errors or breaking changes
- Existing parsing logic remains functional
- AnalystReport structure unchanged
- AgentState integration preserved

## Future Enhancements

Potential improvements based on the research framework:

1. **Multi-Prompt Aggregation**: Implement full SteeringConf methodology for critical decisions
2. **Dynamic Confidence Thresholds**: Adjust based on market conditions
3. **Confidence Tracking**: Monitor confidence calibration accuracy over time
4. **Adaptive Steering**: Adjust cautious steering based on performance feedback
