import sys
import os
from pypdf import PdfReader


def extract_text(pdf_path: str) -> str:
    reader = PdfReader(pdf_path)
    texts = []
    for i, page in enumerate(reader.pages):
        try:
            txt = page.extract_text() or ""
        except Exception as e:
            txt = f"\n[Error extracting page {i}: {e}]\n"
        texts.append(txt)
    return "\n\n".join(texts)


def main():
    if len(sys.argv) < 3:
        print("Usage: python scripts/extract_pdf_text.py <pdf_path> <out_path>")
        sys.exit(2)
    pdf_path = sys.argv[1]
    out_path = sys.argv[2]
    if not os.path.exists(pdf_path):
        print(f"PDF not found: {pdf_path}")
        sys.exit(1)
    text = extract_text(pdf_path)
    os.makedirs(os.path.dirname(out_path), exist_ok=True)
    with open(out_path, "w", encoding="utf-8") as f:
        f.write(text)
    print(f"Wrote extracted text to: {out_path}")


if __name__ == "__main__":
    main()

